import os
import tkinter as tk
from tkinter import filedialog
import glob

#YOLO标注文件类别序号批量修改工具
def select_folder():
    """选择包含标注文件的文件夹"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    folder_path = filedialog.askdirectory(title="请选择包含YOLO标注文件的文件夹")
    root.destroy()
    return folder_path

def get_modification_rules():
    """获取用户输入的修改规则"""
    modification_rules = {}

    while True:
        try:
            # 获取需要修改的序号
            old_class = input("请输入需要修改的序号: ")
            if not old_class.isdigit():
                print("请输入有效的数字！")
                continue
            old_class = int(old_class)

            # 获取修改后的序号
            new_class = input("请输入修改后的序号: ")
            if not new_class.isdigit():
                print("请输入有效的数字！")
                continue
            new_class = int(new_class)

            # 保存修改规则
            modification_rules[old_class] = new_class
            print(f"已添加修改规则: {old_class} -> {new_class}")

            # 询问是否继续
            choice = input("继续输入，请按c并回车，开始转换，请按b并回车: ").lower().strip()

            if choice == 'c':
                continue
            elif choice == 'b':
                break
            else:
                print("无效输入，请输入 'c' 继续或 'b' 开始转换")

        except KeyboardInterrupt:
            print("\n程序已取消")
            return None
        except Exception as e:
            print(f"输入错误: {e}")

    return modification_rules

def modify_label_file(file_path, modification_rules):
    """修改单个标注文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        modified_lines = []
        modified_count = 0

        for line in lines:
            line = line.strip()
            if line:  # 跳过空行
                parts = line.split()
                if len(parts) >= 5:  # YOLO格式至少有5个值
                    class_id = int(parts[0])

                    # 检查是否需要修改
                    if class_id in modification_rules:
                        parts[0] = str(modification_rules[class_id])
                        modified_count += 1

                    modified_lines.append(' '.join(parts) + '\n')
                else:
                    modified_lines.append(line + '\n')
            else:
                modified_lines.append('\n')

        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(modified_lines)

        return modified_count

    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return 0

def batch_modify_labels(folder_path, modification_rules):
    """批量修改文件夹下的所有txt标注文件"""
    if not folder_path or not modification_rules:
        return

    # 查找所有txt文件
    txt_files = glob.glob(os.path.join(folder_path, "*.txt"))

    if not txt_files:
        print("在选定文件夹中未找到任何txt文件")
        return

    print(f"找到 {len(txt_files)} 个txt文件")
    print("开始批量修改...")

    total_modified = 0
    processed_files = 0

    for txt_file in txt_files:
        print(f"正在处理: {os.path.basename(txt_file)}")
        modified_count = modify_label_file(txt_file, modification_rules)

        if modified_count > 0:
            total_modified += modified_count
            processed_files += 1
            print(f"  修改了 {modified_count} 个标签")
        else:
            print(f"  无需修改")

    print(f"\n批量修改完成！")
    print(f"处理文件数: {len(txt_files)}")
    print(f"有修改的文件数: {processed_files}")
    print(f"总共修改标签数: {total_modified}")

def main():
    """主函数"""
    print("YOLO标注文件类别序号批量修改工具")
    print("=" * 40)

    # 选择文件夹
    folder_path = select_folder()
    if not folder_path:
        print("未选择文件夹，程序退出")
        return

    print(f"选择的文件夹: {folder_path}")

    # 获取修改规则
    modification_rules = get_modification_rules()
    if not modification_rules:
        print("未设置修改规则，程序退出")
        return

    print(f"\n修改规则:")
    for old_class, new_class in modification_rules.items():
        print(f"  {old_class} -> {new_class}")

    # 确认执行
    confirm = input("\n确认执行批量修改？(y/n): ").lower().strip()
    if confirm != 'y':
        print("操作已取消")
        return

    # 执行批量修改
    batch_modify_labels(folder_path, modification_rules)

if __name__ == "__main__":
    main()