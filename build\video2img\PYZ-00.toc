('I:\\python code\\yolov11 realtimedect\\build\\video2img\\PYZ-00.pyz',
 [('__future__', 'D:\\Program Files\\anaconda\\Lib\\__future__.py', 'PYMODULE'),
  ('_aix_support',
   'D:\\Program Files\\anaconda\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_compat_pickle',
   'D:\\Program Files\\anaconda\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\Program Files\\anaconda\\Lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Program Files\\anaconda\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime',
   'D:\\Program Files\\anaconda\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal', 'D:\\Program Files\\anaconda\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('_strptime', 'D:\\Program Files\\anaconda\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\Program Files\\anaconda\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\Program Files\\anaconda\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\Program Files\\anaconda\\Lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Program Files\\anaconda\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'D:\\Program Files\\anaconda\\Lib\\base64.py', 'PYMODULE'),
  ('bdb', 'D:\\Program Files\\anaconda\\Lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'D:\\Program Files\\anaconda\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\Program Files\\anaconda\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\Program Files\\anaconda\\Lib\\calendar.py', 'PYMODULE'),
  ('cmd', 'D:\\Program Files\\anaconda\\Lib\\cmd.py', 'PYMODULE'),
  ('code', 'D:\\Program Files\\anaconda\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Program Files\\anaconda\\Lib\\codeop.py', 'PYMODULE'),
  ('concurrent',
   'D:\\Program Files\\anaconda\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Program Files\\anaconda\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Program Files\\anaconda\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Program Files\\anaconda\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Program Files\\anaconda\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('contextlib', 'D:\\Program Files\\anaconda\\Lib\\contextlib.py', 'PYMODULE'),
  ('contextvars',
   'D:\\Program Files\\anaconda\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'D:\\Program Files\\anaconda\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\Program Files\\anaconda\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'D:\\Program Files\\anaconda\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\Program Files\\anaconda\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Program Files\\anaconda\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\Program Files\\anaconda\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\Program Files\\anaconda\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\Program Files\\anaconda\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\Program Files\\anaconda\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\Program Files\\anaconda\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\Program Files\\anaconda\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('dataclasses',
   'D:\\Program Files\\anaconda\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'D:\\Program Files\\anaconda\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\Program Files\\anaconda\\Lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'D:\\Program Files\\anaconda\\Lib\\difflib.py', 'PYMODULE'),
  ('dis', 'D:\\Program Files\\anaconda\\Lib\\dis.py', 'PYMODULE'),
  ('doctest', 'D:\\Program Files\\anaconda\\Lib\\doctest.py', 'PYMODULE'),
  ('email', 'D:\\Program Files\\anaconda\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Program Files\\anaconda\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Program Files\\anaconda\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Program Files\\anaconda\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Program Files\\anaconda\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Program Files\\anaconda\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\Program Files\\anaconda\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Program Files\\anaconda\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Program Files\\anaconda\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Program Files\\anaconda\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Program Files\\anaconda\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Program Files\\anaconda\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Program Files\\anaconda\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\Program Files\\anaconda\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Program Files\\anaconda\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Program Files\\anaconda\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Program Files\\anaconda\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Program Files\\anaconda\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Program Files\\anaconda\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Program Files\\anaconda\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\Program Files\\anaconda\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\Program Files\\anaconda\\Lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'D:\\Program Files\\anaconda\\Lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'D:\\Program Files\\anaconda\\Lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'D:\\Program Files\\anaconda\\Lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'D:\\Program Files\\anaconda\\Lib\\gettext.py', 'PYMODULE'),
  ('glob', 'D:\\Program Files\\anaconda\\Lib\\glob.py', 'PYMODULE'),
  ('gzip', 'D:\\Program Files\\anaconda\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\Program Files\\anaconda\\Lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'D:\\Program Files\\anaconda\\Lib\\hmac.py', 'PYMODULE'),
  ('html', 'D:\\Program Files\\anaconda\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'D:\\Program Files\\anaconda\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('http', 'D:\\Program Files\\anaconda\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'D:\\Program Files\\anaconda\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\Program Files\\anaconda\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'D:\\Program Files\\anaconda\\Lib\\http\\server.py',
   'PYMODULE'),
  ('importlib',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Program Files\\anaconda\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\Program Files\\anaconda\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\Program Files\\anaconda\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'D:\\Program Files\\anaconda\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'D:\\Program Files\\anaconda\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Program Files\\anaconda\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Program Files\\anaconda\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'D:\\Program Files\\anaconda\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'D:\\Program Files\\anaconda\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'D:\\Program Files\\anaconda\\Lib\\mimetypes.py', 'PYMODULE'),
  ('mkl',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\mkl\\__init__.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Program Files\\anaconda\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc', 'D:\\Program Files\\anaconda\\Lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'D:\\Program Files\\anaconda\\Lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'D:\\Program Files\\anaconda\\Lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._utils',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE'),
  ('numpy.array_api',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._indexing_functions',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\array_api\\_indexing_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.dtypes',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE'),
  ('numpy.exceptions',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\Program '
   'Files\\anaconda\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'D:\\Program Files\\anaconda\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'D:\\Program Files\\anaconda\\Lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'D:\\Program Files\\anaconda\\Lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'D:\\Program Files\\anaconda\\Lib\\pickle.py', 'PYMODULE'),
  ('pkgutil', 'D:\\Program Files\\anaconda\\Lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'D:\\Program Files\\anaconda\\Lib\\platform.py', 'PYMODULE'),
  ('pprint', 'D:\\Program Files\\anaconda\\Lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._compat',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile', 'D:\\Program Files\\anaconda\\Lib\\py_compile.py', 'PYMODULE'),
  ('pydoc', 'D:\\Program Files\\anaconda\\Lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'D:\\Program Files\\anaconda\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Program Files\\anaconda\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('queue', 'D:\\Program Files\\anaconda\\Lib\\queue.py', 'PYMODULE'),
  ('quopri', 'D:\\Program Files\\anaconda\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\Program Files\\anaconda\\Lib\\random.py', 'PYMODULE'),
  ('runpy', 'D:\\Program Files\\anaconda\\Lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'D:\\Program Files\\anaconda\\Lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'D:\\Program Files\\anaconda\\Lib\\selectors.py', 'PYMODULE'),
  ('shlex', 'D:\\Program Files\\anaconda\\Lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'D:\\Program Files\\anaconda\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\Program Files\\anaconda\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\Program Files\\anaconda\\Lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'D:\\Program Files\\anaconda\\Lib\\socketserver.py',
   'PYMODULE'),
  ('ssl', 'D:\\Program Files\\anaconda\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics', 'D:\\Program Files\\anaconda\\Lib\\statistics.py', 'PYMODULE'),
  ('string', 'D:\\Program Files\\anaconda\\Lib\\string.py', 'PYMODULE'),
  ('stringprep', 'D:\\Program Files\\anaconda\\Lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'D:\\Program Files\\anaconda\\Lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'D:\\Program Files\\anaconda\\Lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'D:\\Program Files\\anaconda\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\Program Files\\anaconda\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\Program Files\\anaconda\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\Program Files\\anaconda\\Lib\\threading.py', 'PYMODULE'),
  ('threadpoolctl',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\threadpoolctl.py',
   'PYMODULE'),
  ('tkinter',
   'D:\\Program Files\\anaconda\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\Program Files\\anaconda\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\Program Files\\anaconda\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'D:\\Program Files\\anaconda\\Lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\Program Files\\anaconda\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\Program Files\\anaconda\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\Program Files\\anaconda\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'D:\\Program Files\\anaconda\\Lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token', 'D:\\Program Files\\anaconda\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\Program Files\\anaconda\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'D:\\Program Files\\anaconda\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('tty', 'D:\\Program Files\\anaconda\\Lib\\tty.py', 'PYMODULE'),
  ('typing', 'D:\\Program Files\\anaconda\\Lib\\typing.py', 'PYMODULE'),
  ('unittest',
   'D:\\Program Files\\anaconda\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\Program Files\\anaconda\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\Program Files\\anaconda\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\Program Files\\anaconda\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Program Files\\anaconda\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\Program Files\\anaconda\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\Program Files\\anaconda\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\Program Files\\anaconda\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Program Files\\anaconda\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\Program Files\\anaconda\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\Program Files\\anaconda\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib',
   'D:\\Program Files\\anaconda\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\Program Files\\anaconda\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\Program Files\\anaconda\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Program Files\\anaconda\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'D:\\Program Files\\anaconda\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('webbrowser', 'D:\\Program Files\\anaconda\\Lib\\webbrowser.py', 'PYMODULE'),
  ('xml', 'D:\\Program Files\\anaconda\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.parsers',
   'D:\\Program Files\\anaconda\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Program Files\\anaconda\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Program Files\\anaconda\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Program Files\\anaconda\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Program Files\\anaconda\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Program Files\\anaconda\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Program Files\\anaconda\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Program Files\\anaconda\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\Program Files\\anaconda\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Program Files\\anaconda\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('yaml',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE'),
  ('yaml.composer',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE'),
  ('yaml.constructor',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE'),
  ('yaml.cyaml',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE'),
  ('yaml.dumper',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE'),
  ('yaml.emitter',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE'),
  ('yaml.error',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\yaml\\error.py',
   'PYMODULE'),
  ('yaml.events',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\yaml\\events.py',
   'PYMODULE'),
  ('yaml.loader',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\yaml\\loader.py',
   'PYMODULE'),
  ('yaml.nodes',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE'),
  ('yaml.parser',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\yaml\\parser.py',
   'PYMODULE'),
  ('yaml.reader',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\yaml\\reader.py',
   'PYMODULE'),
  ('yaml.representer',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE'),
  ('yaml.resolver',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE'),
  ('yaml.scanner',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE'),
  ('yaml.serializer',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE'),
  ('yaml.tokens',
   'D:\\Program Files\\anaconda\\Lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE'),
  ('zipfile',
   'D:\\Program Files\\anaconda\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'D:\\Program Files\\anaconda\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\Program Files\\anaconda\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE'),
  ('zipimport', 'D:\\Program Files\\anaconda\\Lib\\zipimport.py', 'PYMODULE')])
