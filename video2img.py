import cv2
import os
import tkinter as tk
from tkinter import filedialog, ttk, messagebox, StringVar
from pathlib import Path
import threading

class VideoToImageApp:
    def __init__(self, root):
        self.root = root
        self.root.title("视频转图片工具")
        # 调整窗口初始大小，增加高度
        self.root.geometry("700x500")
        self.root.resizable(True, True)
        
        # 视频文件路径
        self.video_path = None
        
        # 视频参数
        self.video_width = 0
        self.video_height = 0
        self.video_fps = 0
        self.video_frames = 0
        self.video_duration = 0  # 单位：秒
        
        # 自定义提取帧率
        self.extract_fps = StringVar(value="1")
        
        # 添加时间区间变量
        self.start_time = StringVar(value="0")
        self.end_time = StringVar(value="0")
        
        # 转换状态
        self.is_converting = False
        
        # 创建UI
        self.create_ui()
    
    def create_ui(self):
        """创建用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 视频选择区域
        video_select_frame = ttk.LabelFrame(main_frame, text="视频选择", padding=10)
        video_select_frame.pack(fill=tk.X, pady=5)
        
        # 视频路径和选择按钮
        self.video_path_var = StringVar(value="未选择视频文件")
        ttk.Label(video_select_frame, textvariable=self.video_path_var, width=50).pack(side=tk.LEFT, padx=5)
        ttk.Button(video_select_frame, text="选择视频", command=self.select_video).pack(side=tk.RIGHT, padx=5)
        
        # 移除视频信息区域
        
        # 转换设置区域
        convert_frame = ttk.LabelFrame(main_frame, text="转换设置", padding=10)
        convert_frame.pack(fill=tk.X, pady=5)
        
        # 提取帧率设置
        ttk.Label(convert_frame, text="提取帧率:").pack(side=tk.LEFT, padx=5)
        fps_entry = ttk.Entry(convert_frame, textvariable=self.extract_fps, width=5)
        fps_entry.pack(side=tk.LEFT, padx=5)
        ttk.Label(convert_frame, text="帧/秒").pack(side=tk.LEFT)
        
        # 添加视频区间提取设置
        ttk.Label(convert_frame, text="  区间:").pack(side=tk.LEFT, padx=5)
        self.start_time = StringVar(value="0")
        self.end_time = StringVar(value="0")
        ttk.Entry(convert_frame, textvariable=self.start_time, width=5).pack(side=tk.LEFT, padx=2)
        ttk.Label(convert_frame, text="-").pack(side=tk.LEFT)
        ttk.Entry(convert_frame, textvariable=self.end_time, width=5).pack(side=tk.LEFT, padx=2)
        ttk.Label(convert_frame, text="秒").pack(side=tk.LEFT)
        
        # 转换按钮
        self.convert_button = ttk.Button(convert_frame, text="转换为图片", command=self.start_conversion, state=tk.DISABLED)
        self.convert_button.pack(side=tk.RIGHT, padx=5)
        
        # 状态和进度区域
        status_frame = ttk.LabelFrame(main_frame, text="状态", padding=10)
        status_frame.pack(fill=tk.X, pady=5)
        
        # 状态信息
        self.status_var = StringVar(value="就绪")
        ttk.Label(status_frame, textvariable=self.status_var).pack(side=tk.LEFT, padx=5)
        
        # 进度条
        self.progress_var = tk.DoubleVar(value=0.0)
        self.progress_bar = ttk.Progressbar(status_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=5)
        
        # 输出区域 - 增加高度以显示更多信息
        output_frame = ttk.LabelFrame(main_frame, text="输出信息", padding=10)
        output_frame.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # 输出文本框，用于显示详细信息，增加高度
        self.output_text = tk.Text(output_frame, height=12, width=70)
        self.output_text.pack(fill=tk.BOTH, expand=True)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(self.output_text, command=self.output_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.output_text.config(yscrollcommand=scrollbar.set)
        
    def select_video(self):
        """选择视频文件"""
        video_file = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=[
                ("视频文件", "*.mp4 *.avi *.mov *.mkv *.flv *.wmv"),
                ("所有文件", "*.*")
            ]
        )
        
        if not video_file:
            return
        
        self.video_path = video_file
        self.video_path_var.set(os.path.basename(video_file))
        
        # 读取视频信息
        self.read_video_info()
    
    def read_video_info(self):
        """读取视频文件信息"""
        try:
            # 打开视频文件
            cap = cv2.VideoCapture(self.video_path)
            
            if not cap.isOpened():
                self.log_message("错误: 无法打开视频文件")
                return
            
            # 获取视频属性
            self.video_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            self.video_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            self.video_fps = cap.get(cv2.CAP_PROP_FPS)
            self.video_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # 计算时长
            if self.video_fps > 0:
                self.video_duration = self.video_frames / self.video_fps
                # 更新结束时间为视频总时长
                self.end_time.set(str(round(self.video_duration, 2)))
            
            # 启用转换按钮
            self.convert_button.config(state=tk.NORMAL)
            
            # 记录日志
            self.log_message(f"已读取视频信息: {os.path.basename(self.video_path)}")
            self.log_message(f"分辨率: {self.video_width}x{self.video_height}, 帧率: {self.video_fps:.2f} fps")
            self.log_message(f"总帧数: {self.video_frames}, 时长: {self.video_duration:.2f} 秒")
            
            # 释放视频
            cap.release()
            
        except Exception as e:
            self.log_message(f"读取视频信息出错: {str(e)}")
    
    def start_conversion(self):
        """开始转换过程"""
        if self.is_converting:
            return
        
        if not self.video_path:
            messagebox.showwarning("警告", "请先选择视频文件")
            return
        
        try:
            # 获取用户设置的帧率
            extract_fps = float(self.extract_fps.get())
            
            if extract_fps <= 0:
                messagebox.showwarning("警告", "提取帧率必须大于0")
                return
            
            # 获取时间区间
            start_time = float(self.start_time.get())
            end_time = float(self.end_time.get())
            
            # 验证时间区间
            if start_time < 0:
                start_time = 0
                self.start_time.set("0")
            
            if end_time <= 0 or end_time > self.video_duration:
                end_time = self.video_duration
                self.end_time.set(str(round(self.video_duration, 2)))
            
            if start_time >= end_time:
                messagebox.showwarning("警告", "开始时间必须小于结束时间")
                return
                
            # 设置转换状态
            self.is_converting = True
            self.convert_button.config(state=tk.DISABLED)
            self.status_var.set("转换中...")
            self.progress_var.set(0)
            
            # 创建转换线程
            thread = threading.Thread(target=self.convert_video_to_images, args=(extract_fps, start_time, end_time))
            thread.daemon = True
            thread.start()
            
        except ValueError:
            messagebox.showwarning("警告", "请输入有效的帧率和时间区间")
            
    def convert_video_to_images(self, extract_fps, start_time=0, end_time=0):
        """将视频转换为图像序列"""
        try:
            # 创建保存目录
            video_name = os.path.splitext(os.path.basename(self.video_path))[0]
            output_dir = os.path.join(os.path.dirname(self.video_path), video_name)
            
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
                
            self.log_message(f"创建输出目录: {output_dir}")
            
            # 打开视频
            cap = cv2.VideoCapture(self.video_path)
            
            if not cap.isOpened():
                self.log_message("错误: 无法打开视频文件进行转换")
                self.finish_conversion()
                return
            
            # 计算帧提取间隔
            if extract_fps >= self.video_fps:
                # 如果提取帧率大于或等于原始帧率，则提取每一帧
                frame_interval = 1
                self.log_message(f"提取帧率 ({extract_fps}) 大于或等于原始帧率 ({self.video_fps})，将提取每一帧")
            else:
                # 否则，计算间隔
                frame_interval = int(self.video_fps / extract_fps)
                self.log_message(f"将按每 {frame_interval} 帧提取一次，约 {extract_fps:.2f} fps")
            
            # 计算开始和结束帧
            start_frame = int(start_time * self.video_fps)
            end_frame = int(end_time * self.video_fps)
            
            if end_frame <= 0 or end_frame > self.video_frames:
                end_frame = self.video_frames
            
            self.log_message(f"提取时间区间: {start_time:.2f}秒 - {end_time:.2f}秒")
            self.log_message(f"对应帧范围: {start_frame} - {end_frame}")
            
            # 设置视频位置到开始帧
            cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
            
            # 开始提取
            frame_count = start_frame
            saved_count = 0
            
            while frame_count < end_frame:
                ret, frame = cap.read()
                
                if not ret:
                    break
                
                # 根据间隔提取帧
                if (frame_count - start_frame) % frame_interval == 0:
                    # 计算当前帧对应的时间(秒)
                    current_time = frame_count / self.video_fps
                    
                    # 修改保存图像的命名方式，使用视频名_起始时间s结束时间s_序号.jpg
                    image_path = os.path.join(output_dir, f"{video_name}_{int(start_time)}s{int(end_time)}s_{saved_count:06d}.jpg")
                    cv2.imwrite(image_path, frame)
                    saved_count += 1
                    
                    # 更新进度
                    progress = ((frame_count - start_frame) / (end_frame - start_frame)) * 100
                    self.update_progress(progress, f"已提取 {saved_count} 帧")
                
                frame_count += 1
            
            # 完成
            self.log_message(f"转换完成! 共提取 {saved_count} 帧图像")
            self.log_message(f"时间区间: {start_time:.2f}秒 - {end_time:.2f}秒")
            self.log_message(f"输出目录: {output_dir}")
            
            # 添加明显的分隔线和可复制的路径信息
            self.log_message("="*50)
            self.log_message("图片保存位置:")
            self.log_message(f"{output_dir}")
            self.log_message("="*50)
            
            # 释放视频
            cap.release()
            
            # 更新UI
            self.finish_conversion()
            
            # 完成消息
            messagebox.showinfo("完成", f"视频已成功转换为图像!\n共提取 {saved_count} 帧\n时间区间: {start_time:.2f}秒 - {end_time:.2f}秒\n保存至: {output_dir}")
            
        except Exception as e:
            self.log_message(f"转换过程出错: {str(e)}")
            self.finish_conversion()
    
    def update_progress(self, progress_value, status_text):
        """更新进度和状态信息"""
        self.root.after(0, lambda: self.progress_var.set(progress_value))
        self.root.after(0, lambda: self.status_var.set(status_text))
    
    def finish_conversion(self):
        """完成转换，重置状态"""
        self.root.after(0, lambda: self.convert_button.config(state=tk.NORMAL))
        self.root.after(0, lambda: self.status_var.set("就绪"))
        self.root.after(0, lambda: self.progress_var.set(100))
        self.is_converting = False
    
    def log_message(self, message):
        """将消息添加到日志区域"""
        self.root.after(0, lambda: self.output_text.insert(tk.END, message + "\n"))
        self.root.after(0, lambda: self.output_text.see(tk.END))

def main():
    root = tk.Tk()
    app = VideoToImageApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()