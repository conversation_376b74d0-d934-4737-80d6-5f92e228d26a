import cv2
import sys
from ultralytics import YOLO
import tkinter as tk
import torch
from pathlib import Path
import os
from tkinter import filedialog

def choose_input_source():
    print('选择摄像头:1')
    print('选择视频文件:2')
    choice=int(input('输入数字1或者2进行选择').strip())

    if choice==1:
        return 0,'摄像头'
    elif choice==2:
        root=tk.Tk()
        root.withdraw()
        video_path=filedialog.askopenfilename(
            title='选择视频文件',
            filetypes=[('视频文件','*.mp4;*.avi;*.mov'),('所有文件','*.*')]
        )
        if not video_path:
            print('未选择视频文件')
            sys.exit(0)
        return video_path,video_path
    else:
        print('无效输入，程序退出')
        sys.exit(1)

def detect_media():
    model_config={
        'model_path':r'I:\python code\yolov11\yolo11n.pt',
        'model_url':'https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11n.pt'
    }

    predict_config={
        'conf_thre':0.4,
        'iou_thre':0.45,
        'imgsz':640,
        'line_width':2,
        'device':'cuda:0' if torch.cuda.is_available() else 'cpu'
    }

    try:
        input_source,source_desti=choose_input_source()
        cap=cv2.VideoCapture(input_source)
        if isinstance(input_source,int):
            cap.set(cv2.CAP_PROP_FRAME_HEIGHT,720)
            cap.set(cv2.CAP_PROP_FRAME_WIDTH,720)
        if not cap.isOpened():
            raise IOError(f'无法打开视频，请检查')
        save_video=False
        video_writer=None
        output_path=None
        answer=input('是否保存推理视频?(y/n)').strip().lower()
        if answer=='y': 
            save_video=True
            save_dir=os.path.join(os.getcwd(),'predict1')
            '''exist_ok：是否在目录存在时触发异常。如果exist_ok为False（默认值），则在目标目录已存在的情况下触发FileExistsError异常；
            如果exist_ok为True，则在目标目录已存在的情况下不会触发FileExistsError异常。'''
            os.makedirs(save_dir,exist_ok=True)

            frame_width=int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            frame_height=int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fps=cap.get(cv2.CAP_PROP_FPS)
            if fps==0 or fps is None:
                fps=25
            output_path=os.path.join(save_dir,'output_refrence.mp4')
            fourcc=cv2.VideoWriter_fourcc(*'mp4v')
            video_writer=cv2.VideoWriter(output_path,fourcc,fps,(frame_width,frame_height))
            print(f'推理视频保存至：{output_path} ')
        
        if not os.path.exists(model_config['model_path']):
            if model_config['model_url']:
                print('开始下载模型')
                YOLO(model_config['model_url']).download(model_config['model_path'])
            else:
                raise FileNotFoundError(f'模型文件不存在: {model_config["model_path"]}')
        
        # 初始化模型
        model=YOLO(model_config['model_path']).to(predict_config['device'])  #模型加载到指定设备上gpu或者cpu
        print(f'模型加载成功| {model_config["model_path"]},设备：{predict_config["device"].upper()} ')
        print(f'输入来源:{source_desti}')

        #实时循环检测
        while True:
            ret,frame=cap.read()
            if not ret:
                print('视频流结束中断')
                break

            #执行推理
            results=model.predict(
                source=frame,
                stream=True,
                verbose=False,
                conf=predict_config['conf_thre'],
                iou=predict_config['iou_thre'],
                imgsz=predict_config['imgsz'],
                device=predict_config['device']
            )
            #遍历推理结果
            for result in results:
                anotated_frame=result.plot(line_width=predict_config['line_width'])
                break

            if isinstance(input_source,int):
                fps=cap.get(cv2.CAP_PROP_FPS)
                cv2.putText(anotated_frame,f'FPS:{fps:.2f}',(10,30),cv2.FONT_HERSHEY_SIMPLEX,1,(0,255,0),2)
            
            #显示实时画面
            cv2.imshow('yolo 实时检测',anotated_frame)
            #如果保存视频,写入视频文件
            if save_video and video_writer is not None:
                video_writer.write(anotated_frame)


            if cv2.waitKey(1) & 0xff==ord('q'):

                break
            #释放资源
        cap.release()
        if video_writer is not None:
            video_writer.release()
        cv2.destroyAllWindows
        print('检测结束')

        

    except Exception as e:
        print(f"\n❌ 发生错误: {str(e)}")
        print("问题排查建议：")
        print("1. 检查视频源是否正确连接或文件路径是否正确")
        print("2. 确认模型文件路径正确")
        print("3. 检查CUDA是否可用（如需GPU加速）")
        print("4. 尝试降低分辨率设置")

if __name__=='__main__':
    detect_media()
