import os
import random
import shutil
import tkinter as tk
from tkinter import filedialog, ttk, messagebox, StringVar
import glob
import threading # 用于合并过程
import time
from pathlib import Path # 用于路径处理

class DatasetPreparationTool:
    def __init__(self, root):
        self.root = root
        self.root.title("YOLO 数据集工具") # 更改标题
        self.root.geometry("750x650") # 调整窗口大小
        self.root.resizable(True, True)
        
        # 初始化变量 - 数据集准备
        self.source_dir_prepare = StringVar()
        self.valid_ratio = 0.1  # 验证集比例
        self.test_ratio = 0.1   # 测试集比例
        
        # 初始化变量 - 数据集合并
        self.target_dir_merge = StringVar() # 总数据集目录
        self.source_dir_merge = StringVar() # 新数据集目录
        
        # 创建UI
        self.create_ui()
    
    def create_ui(self):
        """创建用户界面"""
        # 使用Notebook实现标签页
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # --- 标签页1: 数据集准备 ---
        prepare_frame = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(prepare_frame, text=' 数据集划分 ')
        self.create_prepare_tab(prepare_frame)
        
        # --- 标签页2: 数据集合并 ---
        merge_frame = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(merge_frame, text=' 数据集合并 ')
        self.create_merge_tab(merge_frame)

        # --- 公共状态和输出区域 ---
        common_status_frame = ttk.LabelFrame(self.root, text="状态与输出", padding=10)
        common_status_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))
        
        self.status_var = StringVar(value="就绪")
        ttk.Label(common_status_frame, textvariable=self.status_var).pack(side=tk.TOP, anchor=tk.W, padx=5)
        
        self.progress_var = tk.DoubleVar(value=0.0)
        self.progress_bar = ttk.Progressbar(common_status_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=5, padx=5)
        
        self.output_text = tk.Text(common_status_frame, height=10)
        self.output_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0,5))
        
        scrollbar = ttk.Scrollbar(self.output_text, command=self.output_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.output_text.config(yscrollcommand=scrollbar.set)

    def create_prepare_tab(self, parent_frame):
        """创建数据集准备标签页的UI元素"""
        # 源目录选择区域
        source_frame = ttk.LabelFrame(parent_frame, text="YOLO数据集原始目录 (包含图像和txt标签)", padding=10) # 更清晰的说明
        source_frame.pack(fill=tk.X, pady=5)
        
        ttk.Label(source_frame, textvariable=self.source_dir_prepare, width=60).pack(side=tk.LEFT, padx=5)
        ttk.Button(source_frame, text="选择目录", command=self.select_source_dir_prepare).pack(side=tk.RIGHT, padx=5)
        
        # 数据集划分比例设置
        ratio_frame = ttk.LabelFrame(parent_frame, text="数据集划分比例 (固定)", padding=10)
        ratio_frame.pack(fill=tk.X, pady=5)
        
        train_ratio = 1 - self.valid_ratio - self.test_ratio
        ttk.Label(ratio_frame, text=f"训练集: {train_ratio:.1%}", width=15).pack(side=tk.LEFT, padx=10) # 增加宽度
        ttk.Label(ratio_frame, text=f"验证集: {self.valid_ratio:.1%}", width=15).pack(side=tk.LEFT, padx=10) # 增加宽度
        ttk.Label(ratio_frame, text=f"测试集: {self.test_ratio:.1%}", width=15).pack(side=tk.LEFT, padx=10) # 增加宽度
        
        # 操作按钮
        button_frame = ttk.Frame(parent_frame, padding=(0, 10))
        button_frame.pack(fill=tk.X, pady=10)
        
        self.prepare_button = ttk.Button(button_frame, text="开始划分数据集", command=self.prepare_dataset, state=tk.DISABLED)
        self.prepare_button.pack(side=tk.RIGHT)
        
    def create_merge_tab(self, parent_frame):
        """创建数据集合并标签页的UI元素"""
        # 总数据集目录选择
        target_frame = ttk.LabelFrame(parent_frame, text="总数据集目录 (目标, 需含train/valid/test结构)", padding=10) # 更清晰的说明
        target_frame.pack(fill=tk.X, pady=5)
        ttk.Label(target_frame, textvariable=self.target_dir_merge, width=60).pack(side=tk.LEFT, padx=5)
        ttk.Button(target_frame, text="选择目录", command=self.select_target_dir_merge).pack(side=tk.RIGHT, padx=5)

        # 新数据集目录选择
        source_merge_frame = ttk.LabelFrame(parent_frame, text="新数据集目录 (源, 需含train/valid/test结构)", padding=10) # 更清晰的说明
        source_merge_frame.pack(fill=tk.X, pady=5)
        ttk.Label(source_merge_frame, textvariable=self.source_dir_merge, width=60).pack(side=tk.LEFT, padx=5)
        ttk.Button(source_merge_frame, text="选择目录", command=self.select_source_dir_merge).pack(side=tk.RIGHT, padx=5)

        # 操作按钮
        button_frame = ttk.Frame(parent_frame, padding=(0, 10))
        button_frame.pack(fill=tk.X, pady=10)
        
        self.merge_button = ttk.Button(button_frame, text="开始合并数据集", command=self.start_merge, state=tk.DISABLED)
        self.merge_button.pack(side=tk.RIGHT)
    
    def select_source_dir_prepare(self):
        """选择用于划分的数据集源目录"""
        directory = filedialog.askdirectory(title="选择YOLO数据集原始目录")
        if directory:
            self.source_dir_prepare.set(directory)
            self.log_message(f"[划分] 已选择原始目录: {directory}")
            # 验证前清除旧日志可能更好
            self.output_text.delete('1.0', tk.END)
            self.validate_prepare_dir(directory)

    def select_target_dir_merge(self):
        """选择用于合并的目标(总)数据集目录"""
        directory = filedialog.askdirectory(title="选择总数据集目录 (合并目标)")
        if directory:
            self.target_dir_merge.set(directory)
            self.log_message(f"[合并] 已选择目标目录: {directory}")
            self.validate_merge_dirs()

    def select_source_dir_merge(self):
        """选择用于合并的源(新)数据集目录"""
        directory = filedialog.askdirectory(title="选择新数据集目录 (合并源)")
        if directory:
            self.source_dir_merge.set(directory)
            self.log_message(f"[合并] 已选择源目录: {directory}")
            self.validate_merge_dirs()

    def validate_prepare_dir(self, directory):
        """验证用于划分的目录 (仅查找当前目录下的文件)"""
        # --- 修正: 仅在当前目录查找，不递归 ---
        image_files = self.get_files_in_current_dir(directory, ['.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff'])
        label_files = [f for f in self.get_files_in_current_dir(directory, [".txt"])
                       if os.path.basename(f).lower() not in ["classes.txt", "data.yaml"]]
        # --- 结束修正 ---

        if not image_files:
            self.log_message("[划分] 警告: 在选择的目录中未找到图像文件")
            self.prepare_button.config(state=tk.DISABLED)
            return
            
        self.log_message(f"[划分] 找到 {len(image_files)} 个图像文件")
        if not label_files:
            self.log_message("[划分] 警告: 未找到标注文件 (.txt)")
            self.prepare_button.config(state=tk.DISABLED) # 没有标签无法进行有效划分
            return # --- 修正: 没有标签文件则禁用按钮并返回 ---
        else:
            self.log_message(f"[划分] 找到 {len(label_files)} 个标注文件")
            # --- 修正: 增加对有效配对数量的检查 ---
            valid_pairs = self.check_file_matches(image_files, label_files, "[划分]")
            if valid_pairs == 0:
                 self.log_message("[划分] 错误: 未找到任何有效的图像-标签配对。无法进行划分。")
                 self.prepare_button.config(state=tk.DISABLED)
                 return
            # --- 结束修正 ---

        self.prepare_button.config(state=tk.NORMAL)
        
    def validate_merge_dirs(self):
        """验证用于合并的两个目录，并更新合并按钮状态"""
        target_dir = self.target_dir_merge.get()
        source_dir = self.source_dir_merge.get()
        
        valid_target = False
        valid_source = False
        
        # 增加日志清除
        self.output_text.delete('1.0', tk.END)
        self.log_message("正在验证合并目录...")

        if target_dir and os.path.isdir(target_dir):
            if self.check_yolo_structure(target_dir, "[合并][目标]"):
                 valid_target = True
        else:
            if target_dir: # 如果路径已设置但不是有效目录
                self.log_message(f"[合并][目标] 错误: 目录 '{target_dir}' 不存在或无效")

        if source_dir and os.path.isdir(source_dir):
             if self.check_yolo_structure(source_dir, "[合并][源]"):
                  valid_source = True
        else:
             if source_dir: # 如果路径已设置但不是有效目录
                self.log_message(f"[合并][源] 错误: 目录 '{source_dir}' 不存在或无效")

        if valid_target and valid_source:
             if os.path.abspath(target_dir) == os.path.abspath(source_dir): # --- 修正: 使用绝对路径比较 ---
                 self.log_message("[合并] 错误: 源目录和目标目录不能相同!")
                 self.merge_button.config(state=tk.DISABLED)
             else:
                 self.log_message("[合并] 目录验证通过，可以开始合并。")
                 self.merge_button.config(state=tk.NORMAL)
        else:
             self.merge_button.config(state=tk.DISABLED)
             
    def check_yolo_structure(self, directory, prefix=""):
        """检查目录是否包含基本的YOLO train/valid/test结构"""
        structure_ok = True
        missing = []
        # 检查 train/valid 即可，test 是可选的
        for split in ['train', 'valid']: # --- 修正: test 是可选的 ---
            for subdir in ['images', 'labels']:
                path_to_check = os.path.join(directory, split, subdir)
                if not os.path.isdir(path_to_check):
                    # 允许 test 目录不存在
                    if split == 'test':
                        continue
                    missing.append(f"{split}/{subdir}")
                    structure_ok = False

        # 单独检查 test 目录（如果存在，其子目录也必须存在）
        test_images_path = os.path.join(directory, 'test', 'images')
        test_labels_path = os.path.join(directory, 'test', 'labels')
        if os.path.isdir(os.path.join(directory, 'test')): # 如果test目录存在
             if not os.path.isdir(test_images_path):
                  missing.append('test/images')
                  structure_ok = False
             if not os.path.isdir(test_labels_path):
                   missing.append('test/labels')
                   structure_ok = False

        if not structure_ok:
            self.log_message(f"{prefix} 错误: 目录 '{os.path.basename(directory)}' 缺少以下结构: {', '.join(missing)}")
        else:
             self.log_message(f"{prefix} 目录 '{os.path.basename(directory)}' 结构检查通过")
        return structure_ok

    def check_file_matches(self, image_files, label_files, prefix=""):
        """检查图像和标签文件的匹配情况，并返回有效配对数量"""
        img_basenames = {os.path.splitext(os.path.basename(f))[0] for f in image_files}
        label_basenames = {os.path.splitext(os.path.basename(f))[0] for f in label_files}
        
        matched_files = img_basenames.intersection(label_basenames)
        valid_pair_count = len(matched_files)
        self.log_message(f"{prefix} 找到 {valid_pair_count} 个有效的图像-标签配对")
        
        missing_labels = img_basenames - label_basenames
        if missing_labels:
            self.log_message(f"{prefix} 警告: {len(missing_labels)} 个图像缺少标注文件 (仅显示前10):")
            for i, missing in enumerate(sorted(missing_labels)[:10]):
                self.log_message(f"  - {missing}")
        
        orphan_labels = label_basenames - img_basenames
        if orphan_labels:
            self.log_message(f"{prefix} 警告: {len(orphan_labels)} 个标注文件缺少图像 (仅显示前10):")
            for i, orphan in enumerate(sorted(orphan_labels)[:10]):
                self.log_message(f"  - {orphan}")
        return valid_pair_count # --- 修正: 返回有效配对数量 ---

    # --- 修正: 新增一个只查找当前目录文件的辅助函数 ---
    def get_files_in_current_dir(self, directory, extensions):
        """获取指定目录下特定扩展名的文件列表 (非递归)"""
        files = []
        if not os.path.isdir(directory):
            return files
        for ext in extensions:
            files.extend(glob.glob(os.path.join(directory, f"*{ext}")))
            files.extend(glob.glob(os.path.join(directory, f"*{ext.upper()}")))
        return list(set(files)) # 去重
    # --- 结束修正 ---

    def prepare_dataset(self):
        """准备数据集"""
        source_dir = self.source_dir_prepare.get()
        if not source_dir or not os.path.isdir(source_dir):
             messagebox.showerror("错误", "请先选择有效的原始数据集目录")
             return

        # --- 修正: 重新获取文件列表并验证 ---
        image_files = self.get_files_in_current_dir(source_dir, ['.jpg', '.jpeg', '.png', '.bmp', '.tif', '.tiff'])
        label_files = [f for f in self.get_files_in_current_dir(source_dir, [".txt"])
                       if os.path.basename(f).lower() not in ["classes.txt", "data.yaml"]]

        if not image_files:
            messagebox.showerror("错误", "未在源目录中找到图像文件")
            return
        if not label_files:
             messagebox.showerror("错误", "未在源目录中找到标签文件")
             return

        img_basenames = {os.path.splitext(os.path.basename(f))[0] for f in image_files}
        label_basenames = {os.path.splitext(os.path.basename(f))[0] for f in label_files}
        matched_basenames = img_basenames.intersection(label_basenames)
        valid_image_files = [f for f in image_files if os.path.splitext(os.path.basename(f))[0] in matched_basenames]
        total_valid_files = len(valid_image_files)

        if total_valid_files == 0:
            messagebox.showerror("错误", "未找到任何有效的图像-标签配对")
            return
        # --- 结束修正 ---

        # 确认操作
        if not messagebox.askyesno("确认划分", # 修改标题
                                  f"将基于 {total_valid_files} 个有效图像-标签对进行划分:\n" # 显示有效数量
                                  f"训练集: {1 - self.valid_ratio - self.test_ratio:.1%}\n"
                                  f"验证集: {self.valid_ratio:.1%}\n"
                                  f"测试集: {self.test_ratio:.1%}\n\n"
                                  f"注意: 这将移动原始文件夹 '{os.path.basename(source_dir)}' 中的有效文件。是否继续?"):
            return

        self.log_message("[划分] 开始准备数据集...")
        self.status_var.set("处理中...")
        self.progress_var.set(0)
        self.prepare_button.config(state=tk.DISABLED)
        self.merge_button.config(state=tk.DISABLED) # 划分时禁用合并

        try:
            # 创建目录结构 (如果需要)
            for split in ['train', 'valid', 'test']:
                for subdir in ['images', 'labels']:
                    os.makedirs(os.path.join(source_dir, split, subdir), exist_ok=True)

            # 随机打乱有效文件列表
            random.shuffle(valid_image_files)

            # 计算划分数量
            valid_count = int(total_valid_files * self.valid_ratio)
            test_count = int(total_valid_files * self.test_ratio)
            if total_valid_files * self.valid_ratio > 0:
                valid_count = max(1, valid_count)
            if total_valid_files * self.test_ratio > 0:
                 test_count = max(1, test_count)
            # 防止 valid+test 超过总数
            if valid_count + test_count > total_valid_files:
                # 按比例缩减，优先保证 valid
                total_ratio = self.valid_ratio + self.test_ratio
                valid_count = max(1, int(total_valid_files * (self.valid_ratio / total_ratio))) if self.valid_ratio > 0 else 0
                test_count = total_valid_files - valid_count # 剩下的给 test (至少为0)

            train_count = total_valid_files - valid_count - test_count

            self.log_message(f"[划分] 找到 {total_valid_files} 个有效的图像-标签对") # 重复日志以便查看
            self.log_message(f"[划分] 计划划分 - 训练集: {train_count} 文件")
            self.log_message(f"[划分] 计划划分 - 验证集: {valid_count} 文件")
            self.log_message(f"[划分] 计划划分 - 测试集: {test_count} 文件")

            # 分配文件列表
            valid_files_list = valid_image_files[:valid_count]
            test_files_list = valid_image_files[valid_count : valid_count + test_count]
            train_files_list = valid_image_files[valid_count + test_count:]

            # 移动文件
            # --- 修正: 使用 start_idx=0 for train ---
            self.move_files_for_prepare(train_files_list, source_dir, os.path.join(source_dir, 'train'), 0, total_valid_files)
            self.move_files_for_prepare(valid_files_list, source_dir, os.path.join(source_dir, 'valid'), train_count, total_valid_files)
            self.move_files_for_prepare(test_files_list, source_dir, os.path.join(source_dir, 'test'), train_count + valid_count, total_valid_files)

            # 创建data.yaml文件
            self.create_data_yaml(source_dir)

            self.log_message("[划分] 数据集准备完成!")
            self.status_var.set("完成")
            self.progress_var.set(100)
            messagebox.showinfo("完成", "数据集划分已成功完成!")

        except Exception as e:
            self.log_message(f"[划分] 处理过程出错: {str(e)}")
            self.status_var.set("出错")
            messagebox.showerror("错误", f"处理过程中发生错误:\n{str(e)}")

        finally:
            self.prepare_button.config(state=tk.NORMAL)
            self.validate_merge_dirs() # 更新合并按钮状态

    def start_merge(self):
        """开始合并数据集的过程"""
        target_dir = self.target_dir_merge.get()
        source_dir = self.source_dir_merge.get()
        
        if not target_dir or not source_dir:
             messagebox.showwarning("警告", "请先选择总数据集和新数据集的目录")
             return
             
        if os.path.abspath(target_dir) == os.path.abspath(source_dir): # 使用绝对路径比较
             messagebox.showerror("错误", "源目录和目标目录不能相同!")
             return
             
        # 再次验证目录结构，以防万一
        if not self.check_yolo_structure(target_dir, "[合并][目标]") or \
           not self.check_yolo_structure(source_dir, "[合并][源]"):
           messagebox.showerror("错误", "请确保源目录和目标目录都具有有效的 train/valid(/test) 结构。")
           return

        # 确认操作
        if not messagebox.askyesno("确认合并",
                                   f"将把 '{os.path.basename(source_dir)}' 的内容合并到 '{os.path.basename(target_dir)}'.\n"
                                   f"同名文件将被重命名后移动。是否继续?"):
             return
             
        self.log_message("[合并] 开始合并数据集...")
        self.status_var.set("合并中...")
        self.progress_var.set(0)
        self.merge_button.config(state=tk.DISABLED)
        self.prepare_button.config(state=tk.DISABLED) # 合并时禁用划分
        
        # 使用线程执行合并操作
        thread = threading.Thread(target=self.merge_datasets, args=(source_dir, target_dir))
        thread.daemon = True
        thread.start()
        
    def merge_datasets(self, source_dir, target_dir):
        """执行数据集合并的核心逻辑"""
        total_files_to_move = 0
        moved_files_count = 0
        renamed_files_count = 0
        error_count = 0
        start_time = time.time()
        files_to_process = [] # 用于进度计算

        try:
            # --- 修正: 预先收集所有需要处理的文件路径 ---
            for split in ['train', 'valid', 'test']:
                for subdir in ['images', 'labels']:
                    source_subdir_path = os.path.join(source_dir, split, subdir)
                    target_subdir_path = os.path.join(target_dir, split, subdir)
                    if os.path.isdir(source_subdir_path):
                        # 确保目标子目录存在
                        os.makedirs(target_subdir_path, exist_ok=True)
                        for filename in os.listdir(source_subdir_path):
                             files_to_process.append({
                                 'source': os.path.join(source_subdir_path, filename),
                                 'target_dir': target_subdir_path,
                                 'filename': filename
                             })
            total_files_to_move = len(files_to_process)
             # --- 结束修正 ---

            if total_files_to_move == 0:
                self.log_message("[合并] 源目录中没有找到需要移动的文件。")
                self.finish_merge(moved_files_count, renamed_files_count, error_count, start_time)
                return

            self.log_message(f"[合并] 准备移动 {total_files_to_move} 个文件...")

            # --- 修正: 遍历预收集的文件列表 ---
            for idx, file_info in enumerate(files_to_process):
                source_file_path = file_info['source']
                target_subdir_path = file_info['target_dir']
                filename = file_info['filename']
                target_file_path = os.path.join(target_subdir_path, filename)

                # 检查文件名冲突
                if os.path.exists(target_file_path):
                    # 重命名逻辑
                    base, ext = os.path.splitext(filename)
                    counter = 1
                    # --- 修正: 限制重命名尝试次数，避免无限循环 ---
                    max_attempts = 1000
                    while os.path.exists(target_file_path) and counter <= max_attempts:
                        new_filename = f"{base}_merged_{counter}{ext}"
                        target_file_path = os.path.join(target_subdir_path, new_filename)
                        counter += 1
                    if counter > max_attempts:
                         self.log_message(f"[合并] 错误: 文件 '{filename}' 重命名尝试次数过多，跳过。")
                         error_count += 1
                         continue # 跳过此文件
                    # --- 结束修正 ---

                    renamed_files_count += 1
                    self.log_message(f"[合并] 文件冲突: '{filename}' 重命名为 '{os.path.basename(target_file_path)}'") # 使用新文件名记录

                # 移动文件
                try:
                    shutil.move(source_file_path, target_file_path)
                    moved_files_count += 1
                except Exception as move_error:
                    self.log_message(f"[合并] 错误: 移动文件 '{filename}' 失败: {move_error}")
                    error_count += 1

                # 更新进度
                # --- 修正: 基于索引更新进度 ---
                progress = ((idx + 1) / total_files_to_move) * 100
                status_text = f"已合并: {idx + 1}/{total_files_to_move}"
                # --- 结束修正 ---
                if error_count > 0:
                    status_text += f" (错误: {error_count})"
                if renamed_files_count > 0:
                     status_text += f" (重命名: {renamed_files_count})" # 添加重命名计数
                self.update_progress(progress, status_text)
            # --- 结束修正 ---

            # 合并完成
            self.log_message(f"[合并] 数据集合并完成!")
            self.finish_merge(moved_files_count, renamed_files_count, error_count, start_time)
            messagebox.showinfo("合并完成", 
                                f"数据集合并已完成。\n"
                                f"成功移动: {moved_files_count} 文件\n"
                                f"重命名: {renamed_files_count} 文件\n"
                                f"移动失败: {error_count} 文件")
                                
        except Exception as e:
            self.log_message(f"[合并] 合并过程中发生严重错误: {str(e)}")
            self.status_var.set("合并出错")
            messagebox.showerror("合并错误", f"合并过程中发生错误:\n{str(e)}")
        finally:
             # 重新启用按钮
             self.root.after(0, lambda: self.merge_button.config(state=tk.NORMAL))
             self.root.after(0, lambda: self.prepare_button.config(state=tk.NORMAL))

    def finish_merge(self, moved, renamed, errors, start_time):
        """完成合并后的清理和日志记录"""
        end_time = time.time()
        duration = end_time - start_time
        self.log_message(f"[合并] --- 合并统计 ---")
        self.log_message(f"[合并] 成功移动文件数: {moved}")
        self.log_message(f"[合并] 因冲突重命名文件数: {renamed}")
        self.log_message(f"[合并] 移动失败文件数: {errors}")
        self.log_message(f"[合并] 总耗时: {duration:.2f} 秒")
        self.root.after(0, lambda: self.status_var.set("合并完成" + (f" (错误: {errors})" if errors else ""))) # 状态栏显示错误数
        self.root.after(0, lambda: self.progress_var.set(100))
        # --- 修正: 合并完成后也需要更新按钮状态 ---
        self.root.after(0, lambda: self.validate_merge_dirs()) # 重新验证以更新按钮状态
        # --- 结束修正 ---

    def create_data_yaml(self, target_dir): # --- 修正: 应该在目标目录创建/更新yaml ---
        """创建或更新目标目录中的 data.yaml 文件"""
        try:
            # --- 修正: 检查目标目录下的 classes.txt ---
            classes_file = os.path.join(target_dir, 'classes.txt')
            class_names = []

            if os.path.exists(classes_file):
                try: # 增加文件读取的异常处理
                    with open(classes_file, 'r', encoding='utf-8') as f: # 指定UTF-8编码
                        class_names = [line.strip() for line in f.readlines() if line.strip()]
                    self.log_message(f"从 {classes_file} 读取了 {len(class_names)} 个类别")
                except Exception as read_err:
                     self.log_message(f"警告: 读取 {classes_file} 文件时出错: {read_err}，将使用空类别列表")
                     class_names = []
            else:
                self.log_message(f"警告: 在目标目录 {target_dir} 未找到 classes.txt 文件，将使用空类别列表")
            # --- 结束修正 ---

            # 使用相对路径以增加可移植性
            train_path = Path('train') / 'images' # 使用 Path 对象
            val_path = Path('valid') / 'images'
            test_path = Path('test') / 'images'

            yaml_content = f"# YOLOv8 dataset configuration file\n"
            yaml_content += f"# Path to dataset directory (relative to this file)\n"
            yaml_content += f"path: .\n\n" # 使用 '.' 表示当前目录
            yaml_content += f"# Train/val/test sets paths (relative to 'path')\n"
            # --- 修正: 使用正斜杠并确保是相对路径 ---
            yaml_content += f"train: {train_path.as_posix()} # ./{train_path.as_posix()}\n"
            yaml_content += f"val: {val_path.as_posix()}   # ./{val_path.as_posix()}\n"

            # test 路径是可选的
            if os.path.exists(os.path.join(target_dir, 'test', 'images')): # --- 修正: 检查目标目录 ---
                 yaml_content += f"test: {test_path.as_posix()}  # (optional) ./{test_path.as_posix()}\n"
            # --- 结束修正 ---

            yaml_content += f"\n"
            yaml_content += f"# Classes\n"
            yaml_content += f"nc: {len(class_names)}  # number of classes\n"
            # 格式化类别名称列表，确保符合YAML格式
            names_str = '[' + ', '.join([f"'{name}'" for name in class_names]) + ']' # 单引号更标准
            yaml_content += f"names: {names_str}  # class names\n"

            # --- 修正: 在目标目录写入yaml ---
            yaml_path = os.path.join(target_dir, 'data.yaml')
            try: # 增加文件写入的异常处理
                with open(yaml_path, 'w', encoding='utf-8') as f: # 指定UTF-8编码
                    f.write(yaml_content)
                self.log_message(f"已创建或更新 data.yaml 文件: {yaml_path}")
                self.log_message(f"类别数量: {len(class_names)}")
                if class_names:
                    self.log_message(f"类别名称: {', '.join(class_names)}")
            except Exception as write_err:
                 self.log_message(f"错误: 写入 data.yaml 文件 ({yaml_path}) 时出错: {write_err}")
                 # 不再 re-raise，避免中断整个流程
            # --- 结束修正 ---

        except Exception as e:
            # 记录更一般的错误
            self.log_message(f"创建data.yaml文件时发生未知错误: {str(e)}")
            # 不再 re-raise

    # 重命名旧的 move_files，避免与合并逻辑混淆
    def move_files_for_prepare(self, image_files, source_dir, target_dir, start_idx, total_valid_files):
        """为数据集划分移动图像和对应的标签文件"""
        processed_count_in_batch = 0
        for i, img_path in enumerate(image_files):
            img_filename = os.path.basename(img_path)
            base_name = os.path.splitext(img_filename)[0]
            # --- 修正: 确保使用原始目录查找标签 ---
            original_label_path = os.path.join(source_dir, f"{base_name}.txt")
            # --- 结束修正 ---
            target_img_path = os.path.join(target_dir, 'images', img_filename)
            target_label_path = os.path.join(target_dir, 'labels', f"{base_name}.txt")

            image_moved = False # 标记图像是否成功移动
            if os.path.exists(img_path):
                try:
                    shutil.move(img_path, target_img_path)
                    image_moved = True # 标记成功
                    # self.log_message(f"[划分] 移动图像: {img_filename}") # 可选：详细日志
                except Exception as e:
                    self.log_message(f"[划分] 错误: 移动图像文件 {img_path} 失败: {e}")
                    continue # 跳过这个文件对
            else:
                self.log_message(f"[划分] 警告: 移动时未找到图像文件 {img_path}")
                continue

            # 仅在图像成功移动后才移动标签
            if image_moved:
                if os.path.exists(original_label_path):
                    try:
                       shutil.move(original_label_path, target_label_path)
                       # self.log_message(f"[划分] 移动标签: {base_name}.txt") # 可选：详细日志
                    except Exception as e:
                         self.log_message(f"[划分] 错误: 移动标签文件 {original_label_path} 失败: {e}")
                         # 图像已移动，标签移动失败，记录错误但继续
                else:
                     # 逻辑上不应发生，因为我们是基于valid_image_files操作的
                     self.log_message(f"[划分] 逻辑错误: 未找到应存在的标签文件 {original_label_path}")

            # 更新进度 (仅在成功处理一对文件后更新)
            current_progress_idx = start_idx + i + 1 # 使用 i 来计算处理的文件数
            progress = (current_progress_idx / total_valid_files) * 100
            self.update_progress(progress, f"[划分] 已处理: {current_progress_idx}/{total_valid_files}")

    def update_progress(self, progress_value, status_text):
        """线程安全地更新进度和状态信息"""
        # 使用 schedule 或 after 来确保在主线程中更新UI
        # --- 修正: 限制进度条值在 0-100 ---
        progress_value = max(0.0, min(100.0, progress_value))
        # --- 结束修正 ---
        try:
            if self.root.winfo_exists(): # 检查窗口是否存在
                self.root.after(0, lambda: self.progress_var.set(progress_value))
                self.root.after(0, lambda: self.status_var.set(status_text))
        except tk.TclError:
             # 窗口可能已关闭
             pass

    def log_message(self, message):
        """线程安全地将消息添加到日志区域"""
        # 使用 schedule 或 after 来确保在主线程中更新UI
        try:
             if self.root.winfo_exists(): # 检查窗口是否存在
                self.root.after(0, lambda: self._log_message_ui(message))
        except tk.TclError:
             # 窗口可能已关闭
             pass

    def _log_message_ui(self, message):
        """实际更新UI日志的方法"""
        # --- 修正: 增加滚动到底部和时间戳 ---
        now = time.strftime("%Y-%m-%d %H:%M:%S")
        self.output_text.insert(tk.END, f"[{now}] {message}\n")
        self.output_text.see(tk.END)
        # --- 结束修正 ---

def main():
    root = tk.Tk()
    app = DatasetPreparationTool(root)
    root.mainloop()

if __name__ == "__main__":
    main()