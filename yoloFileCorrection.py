#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO数据集文件矫正工具
用于检查image文件夹和label文件夹中文件的一致性
确保每个图像文件都有对应的标注文件
"""

import os
import tkinter as tk
from tkinter import filedialog
from pathlib import Path


def select_folder(title):
    """
    弹出文件夹选择对话框

    Args:
        title (str): 对话框标题

    Returns:
        str: 选择的文件夹路径，如果取消则返回None
    """
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口

    folder_path = filedialog.askdirectory(title=title)
    root.destroy()

    return folder_path if folder_path else None


def get_image_files(image_folder):
    """
    获取图像文件夹中的所有图像文件

    Args:
        image_folder (str): 图像文件夹路径

    Returns:
        list: 图像文件名列表（不包含扩展名）
    """
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    image_files = []

    try:
        for file in os.listdir(image_folder):
            file_path = Path(file)
            if file_path.suffix.lower() in image_extensions:
                # 只保存文件名（不包含扩展名）
                image_files.append(file_path.stem)
    except Exception as e:
        print(f"读取图像文件夹时出错: {e}")
        return []

    return image_files


def get_label_files(label_folder):
    """
    获取标注文件夹中的所有txt文件

    Args:
        label_folder (str): 标注文件夹路径

    Returns:
        set: 标注文件名集合（不包含扩展名）
    """
    label_files = set()

    try:
        for file in os.listdir(label_folder):
            if file.lower().endswith('.txt'):
                # 只保存文件名（不包含扩展名）
                label_files.add(Path(file).stem)
    except Exception as e:
        print(f"读取标注文件夹时出错: {e}")
        return set()

    return label_files


def find_missing_labels(image_files, label_files):
    """
    找出没有对应标注文件的图像文件

    Args:
        image_files (list): 图像文件名列表
        label_files (set): 标注文件名集合

    Returns:
        list: 没有对应标注文件的图像文件名列表
    """
    missing_labels = []

    for image_file in image_files:
        if image_file not in label_files:
            missing_labels.append(image_file)

    return missing_labels


def display_results(missing_labels, image_folder, label_folder):
    """
    显示检查结果

    Args:
        missing_labels (list): 缺少标注文件的图像文件列表
        image_folder (str): 图像文件夹路径
        label_folder (str): 标注文件夹路径
    """
    print("\n" + "="*60)
    print("YOLO数据集文件一致性检查结果")
    print("="*60)
    print(f"图像文件夹: {image_folder}")
    print(f"标注文件夹: {label_folder}")
    print("-"*60)

    if not missing_labels:
        print("✅ 所有图像文件都有对应的标注文件！")
        print("数据集文件一致性检查通过。")
    else:
        print(f"❌ 发现 {len(missing_labels)} 个图像文件缺少对应的标注文件：")
        print("\n缺少标注文件的图像文件列表：")
        for i, filename in enumerate(missing_labels, 1):
            print(f"{i:3d}. {filename}")

        print(f"\n建议操作：")
        print("1. 为这些图像文件创建对应的.txt标注文件")
        print("2. 或者从数据集中移除这些图像文件")

    print("="*60)


def main():
    """
    主函数
    """
    print("本工具用于保证YOLO训练文件的一致性，请依次选择image文件夹和label文件夹，按y开始")

    # 等待用户确认
    while True:
        user_input = input().strip().lower()
        if user_input == 'y':
            break
        elif user_input == 'n':
            print("程序已取消。")
            return
        else:
            print("请输入 'y' 开始或 'n' 取消：")

    # 选择图像文件夹
    print("\n请选择图像文件夹...")
    image_folder = select_folder("选择图像文件夹 (images)")

    if not image_folder:
        print("未选择图像文件夹，程序退出。")
        return

    if not os.path.exists(image_folder):
        print(f"图像文件夹不存在: {image_folder}")
        return

    # 选择标注文件夹
    print("请选择标注文件夹...")
    label_folder = select_folder("选择标注文件夹 (labels)")

    if not label_folder:
        print("未选择标注文件夹，程序退出。")
        return

    if not os.path.exists(label_folder):
        print(f"标注文件夹不存在: {label_folder}")
        return

    print(f"\n开始检查文件一致性...")
    print(f"图像文件夹: {image_folder}")
    print(f"标注文件夹: {label_folder}")

    # 获取文件列表
    image_files = get_image_files(image_folder)
    label_files = get_label_files(label_folder)

    if not image_files:
        print("❌ 图像文件夹中没有找到任何图像文件（支持格式：jpg, jpeg, png, bmp, tiff, tif）")
        return

    if not label_files:
        print("❌ 标注文件夹中没有找到任何.txt文件")
        return

    print(f"找到 {len(image_files)} 个图像文件")
    print(f"找到 {len(label_files)} 个标注文件")

    # 查找缺少标注文件的图像
    missing_labels = find_missing_labels(image_files, label_files)

    # 显示结果
    display_results(missing_labels, image_folder, label_folder)


if __name__ == "__main__":
    main()